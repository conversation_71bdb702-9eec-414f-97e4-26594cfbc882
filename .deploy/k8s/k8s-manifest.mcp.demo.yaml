---
kind: Service
apiVersion: v1
metadata:
  name: gauzy-mcp-demo-svc
spec:
  type: ClusterIP
  selector:
    app: gauzy-mcp-demo
  ports:
    - name: http
      protocol: TCP
      port: 443
      targetPort: 3001
    - name: websocket
      protocol: TCP
      port: 3002
      targetPort: 3002

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gauzy-mcp-demo
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gauzy-mcp-demo
  template:
    metadata:
      labels:
        app: gauzy-mcp-demo
    spec:
      containers:
        - name: gauzy-demo-mcp
          image: registry.digitalocean.com/ever/gauzy-mcp:latest
          env:
            # MCP Server Configuration
            - name: MCP_TRANSPORT
              value: "http"
            - name: MCP_SERVER_MODE
              value: "http"
            - name: MCP_HTTP_PORT
              value: "3001"
            - name: MCP_HTTP_HOST
              value: "0.0.0.0"
            - name: MCP_WS_PORT
              value: "3002"
            - name: MCP_WS_HOST
              value: "0.0.0.0"

            # Security and CORS
            - name: MCP_CORS_ORIGIN
              value: "https://mcpdemo.gauzy.co"
            - name: MCP_CORS_CREDENTIALS
              value: "true"

            # Authentication
            - name: MCP_AUTH_ENABLED
              value: "true"
            - name: MCP_AUTH_RESOURCE_URI
              value: "https://mcpdemo.gauzy.co"
            - name: MCP_AUTH_REQUIRED_SCOPES
              value: "mcp.read,mcp.write"
            - name: MCP_AUTH_JWT_AUDIENCE
              value: "https://mcp.gauzy.co"
            - name: MCP_AUTH_JWT_ISSUER
              value: "https://auth.gauzy.co"
            - name: MCP_AUTH_JWT_ALGORITHMS
              value: "RS256,ES256"
            - name: MCP_AUTH_JWT_JWKS_URI
              value: "https://auth.gauzy.co/.well-known/jwks.json"

            # Session Management
            - name: MCP_SESSION_ENABLED
              value: "true"
            - name: MCP_SESSION_COOKIE_NAME
              value: "mcp-session-id"
            - name: MCP_SESSION_TTL
              value: "1800000"
            - name: MCP_AUTH_SESSION_SECRET
              value: "demo-session-secret-key"

            # Rate Limiting
            - name: THROTTLE_ENABLED
              value: "true"
            - name: THROTTLE_TTL
              value: "60000"
            - name: THROTTLE_LIMIT
              value: "100"

            # Gauzy API Integration
            - name: API_BASE_URL
              value: "https://apidemo.gauzy.co"
            - name: GAUZY_AUTO_LOGIN
              value: "true"

          ports:
            - containerPort: 3001
              protocol: TCP
              name: http
            - containerPort: 3002
              protocol: TCP
              name: websocket

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gauzy-mcp-state-ing
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
spec:
  ingressClassName: nginx
  rules:
    - host: mcpdemo.gauzy.co
      http:
        paths:
          - backend:
              service:
                name: gauzy-mcp-demo-svc
                port:
                  number: 443
            path: /
            pathType: Prefix
  tls:
    - hosts:
        - mcpdemo.gauzy.co
      secretName: mcp.gauzy.co-tls
