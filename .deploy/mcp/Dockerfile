# Ever Gauzy MCP

# Transport settings
ARG MCP_TRANSPORT
ARG MCP_SERVER_MODE

# Server configuration
ARG MCP_HTTP_PORT
ARG MCP_HTTP_HOST

# Security settings
ARG MCP_CORS_ORIGIN
ARG MCP_CORS_CREDENTIALS

# AUTH
ARG MCP_AUTH_ENABLED
ARG MCP_AUTH_RESOURCE_URI
ARG MCP_AUTH_REQUIRED_SCOPES
ARG MCP_AUTH_SERVERS

# JSON-encoded array of authorization servers
ARG MCP_AUTH_JWT_AUDIENCE
ARG MCP_AUTH_JWT_ISSUER
ARG MCP_AUTH_JWT_ALGORITHMS

# Token Introspection (Alternative to JWT validation)
ARG MCP_AUTH_INTROSPECTION_ENDPOINT
ARG MCP_AUTH_INTROSPECTION_CLIENT_ID
ARG MCP_AUTH_INTROSPECTION_CLIENT_SECRET

# OAuth caching and performance
ARG MCP_AUTH_TOKEN_CACHE_TTL
ARG MCP_AUTH_METADATA_CACHE_TTL

# OAuth server options
ARG MCP_AUTH_ALLOW_EMBEDDED_SERVER
ARG MCP_AUTH_SESSION_SECRET

# Session management (stored in Redis)
ARG MCP_SESSION_ENABLED
ARG MCP_SESSION_COOKIE_NAME
ARG MCP_SESSION_TTL
ARG REDIS_URL

# Rate limiting and security
ARG THROTTLE_ENABLED
ARG THROTTLE_TTL
ARG THROTTLE_LIMIT
ARG MCP_TRUSTED_PROXIES

# Required Gauzy API settings
ARG API_BASE_URL
ARG GAUZY_AUTH_EMAIL
ARG GAUZY_AUTH_PASSWORD
ARG GAUZY_AUTO_LOGIN

# Server configuration
ARG MCP_WS_PORT
ARG MCP_WS_HOST
ARG MCP_WS_PATH

# TLS/SSL settings (recommended for production)
ARG MCP_WS_TLS
ARG MCP_WS_KEY_PATH
ARG MCP_WS_CERT_PATH

# WebSocket features
ARG MCP_WS_COMPRESSION
ARG MCP_WS_PER_MESSAGE_DEFLATE
ARG MCP_WS_MAX_PAYLOAD

# JSON-encoded array of authorization servers
ARG MCP_AUTH_JWT_JWKS_URI
ARG MCP_AUTH_JWT_PUBLIC_KEY

# Security settings
ARG MCP_WS_ALLOWED_ORIGINS
ARG MCP_WS_SESSION_ENABLED
ARG MCP_WS_SESSION_COOKIE_NAME
ARG MCP_WS_TRUSTED_PROXIES

FROM node:20.18.1-alpine3.19 AS dependencies

LABEL maintainer="<EMAIL>"
LABEL org.opencontainers.image.source="https://github.com/ever-co/ever-gauzy"

RUN npm install yarn -g --force
RUN mkdir /srv/gauzy-mcp && chown -R node:node /srv/gauzy-mcp

USER node:node

WORKDIR /srv/gauzy-mcp

COPY --chown=node:node apps/mcp/package.json ./apps/mcp/
COPY --chown=node:node packages/mcp-server/package.json ./packages/mcp-server/

COPY --chown=node:node lerna.json package.json yarn.lock ./

RUN yarn install --network-timeout 1000000 --frozen-lockfile --ignore-scripts && yarn cache clean


FROM dependencies AS proddependencies

RUN rm -rf /srv/gauzy-mcp/node_modules

RUN yarn install --network-timeout 1000000 --frozen-lockfile --ignore-scripts --production && yarn cache clean

# BUILD
FROM node:20.18.1-alpine3.19 AS build

WORKDIR /srv/gauzy-mcp

# Copy the node_modules from the dependencies stage
COPY --chown=node:node --from=dependencies /srv/gauzy-mcp/node_modules ./node_modules
COPY --chown=node:node --from=dependencies /srv/gauzy-mcp/package.json ./package.json
COPY --chown=node:node apps/mcp ./apps/mcp
COPY --chown=node:node packages/mcp-server ./packages/mcp-server
COPY --chown=node:node tsconfig.base.json tsconfig.json ./

ARG NODE_OPTIONS

ENV NX_NO_CLOUD=true
ENV CI=true

ENV NODE_OPTIONS=${NODE_OPTIONS:-"--max-old-space-size=60000"}
ENV NODE_ENV=${NODE_ENV}
ENV DEMO=${DEMO:-false}
ENV IS_DOCKER=true

RUN rm -rf /srv/gauzy-mcp/dist

RUN yarn build:mcp:prod

# PROD
FROM node:20.18.1-alpine3.19 AS production

WORKDIR /srv/gauzy-mcp

RUN yarn cache clean

# Transport settings
ENV MCP_TRANSPORT=${MCP_TRANSPORT:-http}
ENV MCP_SERVER_MODE=${MCP_SERVER_MODE:-http}

# Server configuration
ENV MCP_HTTP_PORT=${MCP_HTTP_PORT:-3001}
ENV MCP_HTTP_HOST=${MCP_HTTP_HOST:-localhost}

# Security settings
ENV MCP_CORS_ORIGIN=${MCP_CORS_ORIGIN:-http://localhost:3000,http://localhost:4200}
ENV MCP_CORS_CREDENTIALS=${MCP_CORS_CREDENTIALS:-true}

# AUTH
ENV MCP_AUTH_ENABLED=${MCP_AUTH_ENABLED:-true}
ENV MCP_AUTH_RESOURCE_URI=${MCP_AUTH_RESOURCE_URI:-https://mcpdemo.gauzy.co}
ENV MCP_AUTH_REQUIRED_SCOPES=${MCP_AUTH_REQUIRED_SCOPES:-mcp.read,mcp.write}
ENV MCP_AUTH_SERVERS=${MCP_AUTH_SERVERS:-'[]'}

# JSON-encoded array of authorization servers
ENV MCP_AUTH_JWT_AUDIENCE=${MCP_AUTH_JWT_AUDIENCE:-https://mcp.gauzy.co}
ENV MCP_AUTH_JWT_ISSUER=${MCP_AUTH_JWT_ISSUER:-https://auth.gauzy.co}
ENV MCP_AUTH_JWT_ALGORITHMS=${MCP_AUTH_JWT_ALGORITHMS:-RS256,ES256}
# JWT validation options (choose one):
ENV MCP_AUTH_JWT_JWKS_URI=${MCP_AUTH_JWT_JWKS_URI:-https://auth.gauzy.co/.well-known/jwks.json}
ENV MCP_AUTH_JWT_PUBLIC_KEY=${MCP_AUTH_JWT_PUBLIC_KEY}

# Token Introspection (Alternative to JWT validation)
ENV MCP_AUTH_INTROSPECTION_ENDPOINT=${MCP_AUTH_INTROSPECTION_ENDPOINT:-https://auth.gauzy.co/token/introspect}
ENV MCP_AUTH_INTROSPECTION_CLIENT_ID=${MCP_AUTH_INTROSPECTION_CLIENT_ID:-mcp-client}
ENV MCP_AUTH_INTROSPECTION_CLIENT_SECRET=${MCP_AUTH_INTROSPECTION_CLIENT_SECRET:-client-secret}

# OAuth caching and performance
ENV MCP_AUTH_METADATA_CACHE_TTL=${MCP_AUTH_METADATA_CACHE_TTL:-3600}

# OAuth server options
ENV MCP_AUTH_ALLOW_EMBEDDED_SERVER=${MCP_AUTH_ALLOW_EMBEDDED_SERVER:-true}
ENV MCP_AUTH_SESSION_SECRET=${MCP_AUTH_SESSION_SECRET:-your-session-secret-key}

# Session management (stored in Redis)
ENV MCP_SESSION_ENABLED=${MCP_SESSION_ENABLED:-true}
ENV MCP_SESSION_COOKIE_NAME=${MCP_SESSION_COOKIE_NAME:-mcp-session-id}
ENV MCP_SESSION_TTL=${MCP_SESSION_TTL:-1800000}
ENV REDIS_URL=${REDIS_URL:-redis://localhost:6379}

# Rate limiting and security
ENV THROTTLE_ENABLED=${THROTTLE_ENABLED:-true}
ENV THROTTLE_TTL=${THROTTLE_TTL:-60000}
ENV THROTTLE_LIMIT=${THROTTLE_LIMIT:-100}
ENV MCP_TRUSTED_PROXIES=${MCP_TRUSTED_PROXIES:-***********,********}

# Required Gauzy API settings
ENV API_BASE_URL=${API_BASE_URL:-https://apidemo.gauzy.co}
ENV GAUZY_AUTH_EMAIL=${GAUZY_AUTH_EMAIL}
ENV GAUZY_AUTH_PASSWORD=${GAUZY_AUTH_PASSWORD}
ENV GAUZY_AUTO_LOGIN=${GAUZY_AUTO_LOGIN:-true}

# Server configuration
ENV MCP_WS_PORT=${MCP_WS_PORT:-3002}
ENV MCP_WS_HOST=${MCP_WS_HOST:-localhost}
ENV MCP_WS_PATH=${MCP_WS_PATH:-/sse}

# TLS/SSL settings (recommended for production)
ENV MCP_WS_TLS=${MCP_WS_TLS:-true}
ENV MCP_WS_KEY_PATH=${MCP_WS_KEY_PATH}
ENV MCP_WS_CERT_PATH=${MCP_WS_CERT_PATH}

# WebSocket features
ENV MCP_WS_COMPRESSION=${MCP_WS_COMPRESSION:-true}
ENV MCP_WS_PER_MESSAGE_DEFLATE=${MCP_WS_PER_MESSAGE_DEFLATE:-true}
ENV MCP_WS_MAX_PAYLOAD=${MCP_WS_MAX_PAYLOAD:-16777216}

# JSON-encoded array of authorization servers
# OAuth caching and performance
ENV MCP_AUTH_TOKEN_CACHE_TTL=${MCP_AUTH_TOKEN_CACHE_TTL:-300}

# Security settings
ENV MCP_WS_ALLOWED_ORIGINS=${MCP_WS_ALLOWED_ORIGINS:-*}
ENV MCP_WS_SESSION_ENABLED=${MCP_WS_SESSION_ENABLED:-true}
ENV MCP_WS_SESSION_COOKIE_NAME=${MCP_WS_SESSION_COOKIE_NAME:-mcp-ws-session-id}
ENV MCP_WS_TRUSTED_PROXIES=${MCP_WS_TRUSTED_PROXIES:-***********,********}

COPY --chown=node:node --from=proddependencies /srv/gauzy-mcp/node_modules /srv/gauzy-mcp/node_modules
COPY --chown=node:node --from=build /srv/gauzy-mcp/dist .

EXPOSE ${MCP_WS_PORT}
EXPOSE ${MCP_WS_PORT}

CMD ["node", "apps/mcp/main.js"]
